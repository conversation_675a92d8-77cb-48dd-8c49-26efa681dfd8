{"name": "pawcare", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test-backend": "node test-backend-connection.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroui/skeleton": "^2.2.10", "@mui/material": "^7.0.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/vite": "^4.1.4", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^12.7.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.1"}}