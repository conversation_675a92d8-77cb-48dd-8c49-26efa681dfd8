import './App.css'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import About from './Components/About/About.jsx'
import Home from './Components/Home/Home.jsx'
import VetClinics from './Components/VetClinics/VetClinics.jsx'
import Shelter from './Components/Shelter/Shelter.jsx'
import ShopView from './Components/ShopView/ShopView.jsx'
import Login from './Components/Auth/Login.jsx'
import Signup from './Components/Auth/Signup.jsx'
import Cart from './Components/Cart/Cart.jsx'
import ProtectedRoute from './Components/Auth/ProtectedRoute.jsx'
import AppLayout from './Components/Layout/AppLayout.jsx'
import { CartProvider } from './context/CartContext.jsx'
import CatShop from './Components/CatShop/CatShop.jsx'
import Beds from './Components/Beds/Beds.jsx'
import Toys from './Components/Toys/Toys.jsx'
import Bath from './Components/Bath/Bath.jsx'
import Food from './Components/Food/Food.jsx'
import Treats from './Components/Treats/Treats.jsx'
import Furniture from './Components/Furniture/Furniture.jsx'
import SystemAdminDashboard from './Components/Admin/Admin.jsx'
import PageNotFound from './Components/Error/PageNotFound.jsx'
import ErrorBoundary from './Components/Error/ErrorBoundary.jsx'
import CheckoutPage from './components/Payment/CheckoutPage.jsx'
import CheckoutDebug from './components/Payment/CheckoutDebug.jsx'
import PaymentSuccess from './components/Payment/PaymentSuccess.jsx'
import PaymentFailure from './components/Payment/PaymentFailure.jsx'

function App() {
  return (
    <ErrorBoundary>
      <CartProvider>
        <BrowserRouter>
          <AppLayout>
            <Routes>
              {/* Auth Routes */}
              <Route path='/login' element={<Login />} />
              <Route path='/signup' element={<Signup />} />
              <Route path='/' element={<Navigate to="/login" />} />

              {/* Protected Routes */}
              <Route path='/home' element={<ProtectedRoute><Home /></ProtectedRoute>} />
              <Route path='/about' element={<ProtectedRoute><About /></ProtectedRoute>} />
              <Route path='/admin' element={<ProtectedRoute><SystemAdminDashboard /></ProtectedRoute>} />
              <Route path='/vet-clinics' element={<ProtectedRoute><VetClinics /></ProtectedRoute>} />
              <Route path='/shelters' element={<ProtectedRoute><Shelter /></ProtectedRoute>} />
              <Route path='/shopview' element={<ProtectedRoute><ShopView /></ProtectedRoute>} />
              <Route path='/cart' element={<ProtectedRoute><Cart /></ProtectedRoute>} />
              <Route path='/checkout' element={<ProtectedRoute><CheckoutPage /></ProtectedRoute>} />
              <Route path='/checkout-debug' element={<ProtectedRoute><CheckoutDebug /></ProtectedRoute>} />
              <Route path='/payment-success' element={<ProtectedRoute><PaymentSuccess /></ProtectedRoute>} />
              <Route path='/payment-failure' element={<ProtectedRoute><PaymentFailure /></ProtectedRoute>} />
              <Route path='/CatShop' element={<ProtectedRoute><CatShop/></ProtectedRoute>}></Route>
              <Route path='/beds' element={<ProtectedRoute><Beds/></ProtectedRoute>}></Route>
              <Route path='/toys' element={<ProtectedRoute><Toys/></ProtectedRoute>}></Route>
              <Route path='/bath' element={<ProtectedRoute><Bath/></ProtectedRoute>}></Route>
              <Route path='/food' element={<ProtectedRoute><Food/></ProtectedRoute>}></Route>
              <Route path='/treats' element={<ProtectedRoute><Treats/></ProtectedRoute>}></Route>
              <Route path='/furniture' element={<ProtectedRoute><Furniture/></ProtectedRoute>}></Route>

              {/* Catch-all route for 404 errors */}
              <Route path='*' element={<PageNotFound />} />
            </Routes>
          </AppLayout>
        </BrowserRouter>
      </CartProvider>
    </ErrorBoundary>
  )
}

export default App
