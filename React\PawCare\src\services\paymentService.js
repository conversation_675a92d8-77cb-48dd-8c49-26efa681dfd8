import axios from 'axios';
import { Payment_BaseUrl } from '../Components/Url/BaseUrl';
import {
  Add_PaymentIntentUrl,
  Add_ConfirmPaymentUrl,
  Add_CreateCustomerUrl,
  Add_PaymentHistoryUrl
} from '../Components/Url/EndUrl';

// Create axios instance with base configuration for payment API
const paymentAPI = axios.create({
  baseURL: Payment_BaseUrl, // http://localhost:3000/api/v0/payment
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 second timeout for payment operations
});

// Add request interceptor to include auth token if available
paymentAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('isAuthenticated');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Payment Service Functions
export const paymentService = {
  // Create Payment Intent - Matches API documentation format
  createPaymentIntent: async (paymentData) => {
    try {
      // Ensure the request matches the documented API format
      const requestBody = {
        amount: paymentData.amount, // Amount in cents
        currency: paymentData.currency || 'usd',
        description: paymentData.description || 'Payment for services'
      };

      console.log('Creating payment intent with:', requestBody);

      const response = await paymentAPI.post(Add_PaymentIntentUrl, requestBody);

      // Validate response matches documented format
      if (response.data.success && response.data.data) {
        console.log('Payment intent created successfully:', response.data.data);
        return response.data;
      } else {
        throw new Error('Invalid response format from payment API');
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);

      // Handle specific error cases as documented
      if (error.response?.status === 404) {
        throw new Error('Payment endpoint not found. Please ensure your backend is running at /api/v0/payment/create-payment-intent');
      }

      if (error.response?.status === 400) {
        const errorMsg = error.response?.data?.message || 'Invalid payment data. Amount must be at least $0.50';
        throw new Error(errorMsg);
      }

      if (error.response?.status === 500) {
        const errorMsg = error.response?.data?.message || 'Server error. Please check your Stripe configuration.';
        throw new Error(errorMsg);
      }

      if (!error.response) {
        throw new Error('Unable to connect to payment server. Please check if your backend is running on localhost:3000');
      }

      // Return the actual error from backend
      const backendError = error.response?.data;
      if (backendError && !backendError.success) {
        throw new Error(backendError.message || backendError.error || 'Payment initialization failed');
      }

      throw new Error(error.message || 'Payment initialization failed');
    }
  },

  // Confirm Payment - Matches API documentation format
  confirmPayment: async (paymentIntentId) => {
    try {
      const requestBody = {
        paymentIntentId: paymentIntentId
      };

      console.log('Confirming payment with ID:', paymentIntentId);

      const response = await paymentAPI.post(Add_ConfirmPaymentUrl, requestBody);

      // Validate response matches documented format
      if (response.data.success && response.data.data) {
        console.log('Payment confirmation successful:', response.data.data);
        return response.data;
      } else {
        throw new Error('Invalid response format from payment confirmation API');
      }
    } catch (error) {
      console.error('Error confirming payment:', error);

      if (error.response?.status === 404) {
        throw new Error('Payment confirmation endpoint not found. Please ensure your backend is configured.');
      }

      if (error.response?.status === 400) {
        const errorMsg = error.response?.data?.message || 'Invalid payment intent ID provided.';
        throw new Error(errorMsg);
      }

      // Return the actual error from backend
      const backendError = error.response?.data;
      if (backendError && !backendError.success) {
        throw new Error(backendError.message || backendError.error || 'Payment confirmation failed');
      }

      throw new Error(error.message || 'Payment confirmation failed');
    }
  },

  // Create Customer
  createCustomer: async (customerData) => {
    try {
      const response = await paymentAPI.post(Add_CreateCustomerUrl, customerData);
      return response.data;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw error.response?.data || error.message;
    }
  },

  // Get Payment History
  getPaymentHistory: async (limit = 10) => {
    try {
      const response = await paymentAPI.get(`${Add_PaymentHistoryUrl}?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error.response?.data || error.message;
    }
  }
};

export default paymentService;
