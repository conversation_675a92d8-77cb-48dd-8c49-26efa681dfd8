import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import getStripe from '../../config/stripe';

const StripeProvider = ({ children }) => {
  const stripePromise = getStripe();

  const options = {
    // You can customize the appearance of Elements here
    appearance: {
      theme: 'stripe',
      variables: {
        colorPrimary: '#575CEE',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        fontFamily: 'system-ui, sans-serif',
        spacingUnit: '4px',
        borderRadius: '8px',
      },
    },
  };

  return (
    <Elements stripe={stripePromise} options={options}>
      {children}
    </Elements>
  );
};

export default StripeProvider;
