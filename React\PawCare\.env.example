# PawCare React App Environment Variables (Vite)
# Copy this file to .env and fill in your actual values

# Stripe Configuration
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/
# Note: Vite uses VITE_ prefix for environment variables
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51RSxcXQkL9Zh5RwU8N6bzjsqGmgobmJGE0R55TceiDypE7EtTXICzSeYtld9pe60JTtsQmyQerLQFFYdqYN9pYpi00weG1Vox4

# Backend API Configuration
VITE_API_BASE_URL=http://localhost:3000/api/v0

# Development Settings
VITE_ENVIRONMENT=development

# Instructions:
# 1. Copy this file to .env: cp .env.example .env
# 2. Replace the placeholder values with your actual Stripe keys
# 3. Make sure your backend is configured with the corresponding secret key
# 4. Never commit your .env file to version control
# 5. Vite requires VITE_ prefix for environment variables to be accessible in the browser
