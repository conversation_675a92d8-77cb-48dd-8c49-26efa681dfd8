import React from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { FaExclamationTriangle, FaArrowLeft, FaRedo } from 'react-icons/fa';

const PaymentFailure = () => {
  const [searchParams] = useSearchParams();
  const errorMessage = searchParams.get('error_message') || 'Your payment could not be processed.';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <FaExclamationTriangle className="text-6xl text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Payment Failed
          </h1>
          <p className="text-gray-600">
            {errorMessage}
          </p>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-red-800 mb-2">What happened?</h3>
          <ul className="text-sm text-red-700 text-left space-y-1">
            <li>• Your payment method may have been declined</li>
            <li>• There might be insufficient funds</li>
            <li>• Your card information may be incorrect</li>
            <li>• There could be a temporary network issue</li>
          </ul>
        </div>

        <div className="space-y-3">
          <p className="text-sm text-gray-600">
            Don't worry! Your cart items are still saved. You can try again or use a different payment method.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Link
              to="/cart"
              className="flex-1 bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] transition-colors flex items-center justify-center"
            >
              <FaRedo className="mr-2" />
              Try Again
            </Link>
            
            <Link
              to="/home"
              className="flex-1 border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              <FaArrowLeft className="mr-2" />
              Continue Shopping
            </Link>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="font-semibold text-gray-900 mb-2">Need Help?</h4>
          <p className="text-xs text-gray-500 mb-3">
            If you continue to experience issues, please contact our support team.
          </p>
          <div className="text-sm text-gray-600">
            <p>Email: <EMAIL></p>
            <p>Phone: 1-800-PAW-CARE</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailure;
