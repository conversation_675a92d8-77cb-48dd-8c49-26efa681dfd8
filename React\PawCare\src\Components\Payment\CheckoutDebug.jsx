import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../context/CartContext';

const CheckoutDebug = () => {
  const { cartItems, cartTotal } = useCart();

  console.log('CheckoutDebug - Cart data:', {
    cartItems,
    cartTotal,
    cartItemsLength: cartItems?.length
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">Checkout Debug Page</h1>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Cart Information:</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Cart Items Count:</strong> {cartItems?.length || 0}</p>
            <p><strong>Cart Total:</strong> ${cartTotal?.toFixed(2) || '0.00'}</p>
            <p><strong>Cart Items:</strong></p>
            <pre className="text-sm mt-2 overflow-auto">
              {JSON.stringify(cartItems, null, 2)}
            </pre>
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-2">Environment Check:</h2>
          <div className="bg-gray-100 p-4 rounded">
            <p><strong>Current URL:</strong> {window.location.href}</p>
            <p><strong>Environment Mode:</strong> {import.meta.env.MODE}</p>
            <p><strong>Stripe Key Set:</strong> {import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY ? 'Yes' : 'No (using fallback)'}</p>
          </div>
        </div>

        <div className="space-y-3">
          <Link
            to="/checkout"
            className="w-full block text-center bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] transition-colors"
          >
            Try Real Checkout Page
          </Link>

          <Link
            to="/cart"
            className="w-full block text-center border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
          >
            Back to Cart
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CheckoutDebug;
