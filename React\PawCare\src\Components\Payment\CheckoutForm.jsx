import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useCart } from '../../context/CartContext';
import { paymentService } from '../../services/paymentService';
import { FaLock, FaSpinner } from 'react-icons/fa';

const CheckoutForm = ({ onSuccess, onError }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { cartTotal, cartItems, clearCart } = useCart();

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [clientSecret, setClientSecret] = useState('');

  // Create payment intent when component mounts
  useEffect(() => {
    if (cartTotal > 0) {
      createPaymentIntent();
    }
  }, [cartTotal]);

  const createPaymentIntent = async () => {
    try {
      setIsLoading(true);

      // Convert cart total to cents as required by API documentation
      // Example: $20.00 = 2000 cents
      const amount = Math.round(cartTotal * 100);

      // Validate minimum amount (Stripe requires minimum $0.50 = 50 cents)
      if (amount < 50) {
        throw new Error('Order total must be at least $0.50');
      }

      const paymentData = {
        amount: amount, // Amount in cents as documented
        currency: 'usd',
        description: `PawCare Order - ${cartItems.length} items`
      };

      console.log('Creating payment intent for amount:', amount, 'cents ($' + (amount/100).toFixed(2) + ')');

      const response = await paymentService.createPaymentIntent(paymentData);

      if (response.success && response.data.clientSecret) {
        setClientSecret(response.data.clientSecret);
      } else {
        const errorMessage = response.message || 'Failed to initialize payment';
        setMessage(errorMessage);
        // Don't call onError here - let the user see the error and try again
        console.error('Payment initialization failed:', errorMessage);
      }
    } catch (error) {
      console.error('Error creating payment intent:', error);
      setMessage(`Payment initialization failed: ${error.message || 'Please check if your backend is running and try again.'}`);
      // Don't call onError here - let the user see the error and try again
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: 'if_required'
      });

      if (error) {
        setMessage(error.message);
        onError && onError(error);
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment successful
        setMessage('Payment successful!');
        clearCart(); // Clear the cart after successful payment
        onSuccess && onSuccess(paymentIntent);
      } else if (paymentIntent && paymentIntent.status === 'processing') {
        setMessage('Payment is processing. Please wait...');
      } else if (paymentIntent && paymentIntent.status === 'requires_action') {
        setMessage('Payment requires additional authentication.');
      }
    } catch (error) {
      console.error('Payment error:', error);
      setMessage('An unexpected error occurred during payment processing.');
      onError && onError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while initializing payment
  if (!clientSecret && isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-2xl text-[#575CEE]" />
        <span className="ml-2">Initializing payment...</span>
      </div>
    );
  }

  // Show error state if payment initialization failed
  if (!clientSecret && !isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="mb-4">
          <svg className="h-12 w-12 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Initialization Failed</h3>
          {message && (
            <p className="text-red-600 mb-4">{message}</p>
          )}
          <p className="text-gray-600 mb-4">
            Unable to initialize payment. This could be due to:
          </p>
          <ul className="text-sm text-gray-500 text-left max-w-md mx-auto mb-6">
            <li>• Backend server not running</li>
            <li>• Payment routes not configured</li>
            <li>• Network connectivity issues</li>
            <li>• Stripe configuration problems</li>
          </ul>
        </div>
        <div className="space-y-3">
          <button
            onClick={createPaymentIntent}
            className="w-full bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] transition-colors"
          >
            Try Again
          </button>
          <Link
            to="/cart"
            className="w-full block text-center border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
          >
            Back to Cart
          </Link>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">Order Summary</h3>
        <div className="flex justify-between items-center">
          <span>Total ({cartItems.length} items)</span>
          <span className="font-bold text-[#575CEE]">${cartTotal.toFixed(2)}</span>
        </div>
      </div>

      <div className="space-y-4">
        <PaymentElement
          id="payment-element"
          options={{
            layout: "tabs",
            variables: {
              colorPrimary: '#575CEE',
            }
          }}
        />
      </div>

      {message && (
        <div className={`p-3 rounded-md text-sm ${
          message.includes('successful')
            ? 'bg-green-100 text-green-700'
            : 'bg-red-100 text-red-700'
        }`}>
          {message}
        </div>
      )}

      <button
        disabled={isLoading || !stripe || !elements}
        type="submit"
        className="w-full bg-[#575CEE] text-white py-3 px-4 rounded-md hover:bg-[#4a4fd1] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
      >
        {isLoading ? (
          <>
            <FaSpinner className="animate-spin mr-2" />
            Processing...
          </>
        ) : (
          <>
            <FaLock className="mr-2" />
            Pay ${cartTotal.toFixed(2)}
          </>
        )}
      </button>

      <div className="text-center text-sm text-gray-500">
        <FaLock className="inline mr-1" />
        Your payment information is secure and encrypted
      </div>
    </form>
  );
};

export default CheckoutForm;
